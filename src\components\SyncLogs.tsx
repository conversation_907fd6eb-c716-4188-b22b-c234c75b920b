import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchSyncLogs,
  retrySyncOperation,
} from "@/store/actions/syncLogs.actions";
import { setFilters, setSelectedLog } from "@/store/slices/syncLogsSlice";
import {
  Calendar,
  Search,
  Filter,
  Refresh<PERSON><PERSON>,
  CheckCircle,
  Alert<PERSON>riangle,
  Clock,
  FileText,
  Copy,
} from "lucide-react";

const SyncLogs: React.FC = () => {
  const dispatch = useAppDispatch();
  const { filteredLogs, filters, selectedLog, isLoading } = useAppSelector(
    (state) => state.syncLogs
  );
  const { selectedOrganization } = useAppSelector((state) => state.companies);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // Fetch logs on component mount and when organization changes
  useEffect(() => {
    if (selectedOrganization) {
      dispatch(fetchSyncLogs({ ...filters, companyId: selectedOrganization }));
    }
  }, [dispatch, filters, selectedOrganization]);

  // Filter handlers
  const handleFilterChange = (filterType: string, value: string) => {
    dispatch(setFilters({ [filterType]: value }));
  };

  const handleRefresh = () => {
    if (selectedOrganization) {
      dispatch(fetchSyncLogs({ ...filters, companyId: selectedOrganization }));
    }
  };

  const handleLogClick = (log: any) => {
    dispatch(setSelectedLog(log));
    setIsDrawerOpen(true);
  };

  const handleRetry = async (logId: string) => {
    try {
      await dispatch(retrySyncOperation({ logId })).unwrap();
    } catch (error) {
      console.error("Retry failed:", error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "success":
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            Success
          </Badge>
        );
      case "error":
        return <Badge variant="destructive">Error</Badge>;
      case "warning":
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
            Warning
          </Badge>
        );
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const handleRowClick = (log: any) => {
    handleLogClick(log);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Sync Logs</h2>
          <p className="text-muted-foreground">
            Monitor synchronization history and troubleshoot issues
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
          <CardDescription>
            Filter sync logs by date range, entity, and status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Start Date</label>
              <Input
                type="date"
                value={filters.startDate}
                onChange={(e) =>
                  handleFilterChange("startDate", e.target.value)
                }
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">End Date</label>
              <Input
                type="date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange("endDate", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Entity</label>
              <Select
                value={filters.endpoint}
                onValueChange={(value) => handleFilterChange("endpoint", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Entities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Entities</SelectItem>
                  <SelectItem value="Accounts">Accounts</SelectItem>
                  <SelectItem value="Bank Transactions">
                    Bank Transactions
                  </SelectItem>
                  <SelectItem value="Journals">Journals</SelectItem>
                  <SelectItem value="Invoices">Invoices</SelectItem>
                  <SelectItem value="Payments">Payments</SelectItem>
                  <SelectItem value="Manual Journals">
                    Manual Journals
                  </SelectItem>
                  <SelectItem value="Credit Notes">Credit Notes</SelectItem>
                  <SelectItem value="Tracking Categories">
                    Tracking Categories
                  </SelectItem>
                  <SelectItem value="Tax Rates">Tax Rates</SelectItem>
                  <SelectItem value="Attachments">Attachments</SelectItem>
                  <SelectItem value="Reports (Balance Sheet, Profit and Loss, Trial Balance)">
                    Reports (Balance Sheet, Profit and Loss, Trial Balance)
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select
                value={filters.status}
                onValueChange={(value) => handleFilterChange("status", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="success">Success</SelectItem>
                  <SelectItem value="warning">Warning</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search logs..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Sync History
              </CardTitle>
              <CardDescription>
                Showing {filteredLogs.length} log entries - Click any row to
                view details
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
              />
              {isLoading ? "Loading..." : "Refresh"}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Timestamp</TableHead>
                <TableHead>Entity</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Message</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLogs.map((log) => (
                <TableRow
                  key={log.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleRowClick(log)}
                >
                  <TableCell className="font-mono text-sm">
                    {log.timestamp}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      {log.endpoint}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(log.status)}
                      {getStatusBadge(log.status)}
                    </div>
                  </TableCell>
                  <TableCell>{log.duration}</TableCell>
                  <TableCell className="max-w-xs truncate">
                    {log.message}
                  </TableCell>
                  <TableCell>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRetry(log.id);
                      }}
                      disabled={isLoading}
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Retry
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Sheet open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
        <SheetContent side="right" className="w-[80vw] sm:w-[80vw] max-w-none">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {selectedLog?.endpoint}
            </SheetTitle>
            <SheetDescription>
              GET v1/{selectedLog?.endpoint.toLowerCase()}
            </SheetDescription>
          </SheetHeader>

          {selectedLog && (
            <div className="mt-6 space-y-6">
              <div className="flex justify-end">
                <Button
                  onClick={() => handleRetry(selectedLog.id)}
                  className="bg-blue-500 hover:bg-blue-600"
                  disabled={isLoading}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Resend Request
                </Button>
              </div>

              <div className="grid grid-cols-3 gap-6">
                <div>
                  <h4 className="font-semibold text-sm mb-2">Integration</h4>
                  <p className="text-sm">Xero</p>
                </div>
                <div>
                  <h4 className="font-semibold text-sm mb-2">API</h4>
                  <p className="text-sm">
                    {selectedLog.endpoint.toLowerCase()}
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-sm mb-2">Timestamp</h4>
                  <p className="text-sm">{selectedLog.timestamp}</p>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-6">
                <div>
                  <h4 className="font-semibold text-sm mb-2">Status</h4>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">
                      {selectedLog.status === "success"
                        ? "200"
                        : selectedLog.status === "error"
                        ? "429"
                        : "206"}
                    </span>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-sm mb-2">Duration</h4>
                  <p className="text-sm">
                    {selectedLog.duration.replace("min", "")}ms
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-sm mb-2">Full URL</h4>
                  <p className="text-sm truncate">{selectedLog.fullUrl}</p>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-sm mb-2">Request ID</h4>
                <p className="text-sm font-mono">{selectedLog.requestId}</p>
              </div>

              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-sm">METHOD & ENDPOINT</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        copyToClipboard(
                          `GET v1/${selectedLog.endpoint.toLowerCase()}`
                        )
                      }
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy
                    </Button>
                  </div>
                  <div className="bg-muted p-3 rounded">
                    <span className="text-blue-500 font-semibold">GET</span> v1/
                    {selectedLog.endpoint.toLowerCase()}
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-sm">HEADERS</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        copyToClipboard("content-type:\napplication/json")
                      }
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy
                    </Button>
                  </div>
                  <div className="bg-muted p-3 rounded">
                    <div className="text-sm">
                      <div>
                        <strong>content-type:</strong>
                      </div>
                      <div>application/json</div>
                    </div>
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-sm">RESPONSE BODY</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        copyToClipboard(selectedLog.responseBody || "")
                      }
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy
                    </Button>
                  </div>
                  <div className="bg-muted p-3 rounded max-h-40 overflow-y-auto">
                    <pre className="text-sm whitespace-pre-wrap">
                      {JSON.stringify(
                        JSON.parse(selectedLog.responseBody || "{}"),
                        null,
                        2
                      )}
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          )}
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default SyncLogs;
