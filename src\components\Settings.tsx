import React, { useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  RefreshCw,
  Play,
  CheckCircle,
  Clock,
  Calendar,
  Database,
} from "lucide-react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { getSyncHistory, triggerSync } from "@/store/actions/sync.actions";
import useOrganizations from "@/hooks/useOrganizations";

const Settings: React.FC = () => {
  const dispatch = useAppDispatch();
  const { entities, isLoading } = useAppSelector((state) => state.settings);
  const { selectedOrganization } = useOrganizations();

  // Fetch settings on component mount
  useEffect(() => {
    if (selectedOrganization) {
      dispatch(getSyncHistory({ companyId: selectedOrganization }));
    }
  }, [dispatch, selectedOrganization]);

  const handleSyncEntity = (entityName: string) => {
    console.log(`Triggering sync for entity: ${entityName}`);

    if (!selectedOrganization) {
      console.error("No organization selected");
      return;
    }
    if (entityName === "Reports") {
      entities =[]
    }

    const payload = {
      companyId: selectedOrganization,
      entities: [entityName],
      priority: "NORMAL" as const,
      fullSync: false,
    };
    console.log("Payload:", payload);
    dispatch(triggerSync(payload));
  };

  const handleSyncAll = async () => {
    try {
      if (!selectedOrganization) {
        console.error("No organization selected");
        return;
      }

      const payload = {
        companyId: selectedOrganization,
        entities: entities.filter((e) => e.enabled).map((e) => e.name),
        priority: "NORMAL" as const,
        fullSync: true,
      };
      await dispatch(triggerSync(payload)).unwrap();
    } catch (error) {
      console.error("Sync all failed:", error);
    }
  };

  return (
    <div className="space-y-8">
      {/* Sync Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border border-amber-200 shadow-sm bg-gradient-to-br from-white to-amber-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Entities
                </p>
                <p className="text-3xl font-bold text-gray-800">
                  {entities.length}
                </p>
              </div>
              <div className="p-3 bg-amber-100 rounded-full">
                <Database className="h-6 w-6 text-gray-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-amber-200 shadow-sm bg-gradient-to-br from-emerald-50 to-emerald-100">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-emerald-600">
                  Last Full Sync
                </p>
                <p className="text-lg font-semibold text-emerald-800">
                  2024-06-12
                </p>
                <p className="text-sm text-emerald-700">10:35:00</p>
              </div>
              <div className="p-3 bg-emerald-200 rounded-full">
                <CheckCircle className="h-6 w-6 text-emerald-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-amber-200 shadow-sm bg-gradient-to-br from-orange-50 to-orange-100">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600">
                  Next Scheduled
                </p>
                <p className="text-lg font-semibold text-orange-800">Today</p>
                <p className="text-sm text-orange-700">18:00:00</p>
              </div>
              <div className="p-3 bg-orange-200 rounded-full">
                <Calendar className="h-6 w-6 text-orange-700" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Entity Synchronization */}
      <Card className="border border-amber-200 shadow-sm">
        <CardHeader className="pb-4 bg-gradient-to-r from-white to-amber-50 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-bold text-gray-800">
                Entity Synchronization
              </CardTitle>
              <CardDescription className="text-gray-600 mt-2">
                Monitor and manage data synchronization for all entities.
                Automatic sync runs on schedule, with manual options available.
              </CardDescription>
            </div>
            <Button
              onClick={handleSyncAll}
              className="bg-gray-700 hover:bg-gray-800 text-white shadow-md transition-all duration-200 hover:shadow-lg"
              size="lg"
              disabled={isLoading}
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
              />
              {isLoading ? "Syncing..." : "Sync All Entities"}
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="border border-amber-200 rounded-lg overflow-hidden bg-white">
            <Table>
              <TableHeader>
                <TableRow className="bg-amber-50 border-b-2 border-amber-200">
                  <TableHead className="w-[400px] font-semibold text-gray-700 py-4 px-6">
                    Entity
                  </TableHead>
                  <TableHead className="w-[200px] font-semibold text-gray-700 py-4">
                    Last Sync
                  </TableHead>
                  <TableHead className="w-[120px] font-semibold text-gray-700 py-4">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {entities.map((entity, index) => (
                  <TableRow
                    key={entity.name}
                    className="hover:bg-amber-50 transition-colors border-b border-amber-100"
                  >
                    <TableCell className="font-medium text-gray-800 py-4 px-6">
                      {entity.name}
                    </TableCell>
                    <TableCell className="font-mono text-sm text-gray-600 py-4">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-gray-400" />
                        {entity.lastSync}
                      </div>
                    </TableCell>
                    <TableCell className="py-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          handleSyncEntity(
                            entity.name ===
                              "Reports (P&L, Balance Sheet, Trial Balance)"
                              ? "Reports"
                              : entity.name
                          )
                        }
                        disabled={entity.status === "syncing" || isLoading}
                        className="border-amber-300 text-gray-700 hover:bg-amber-50 hover:border-amber-400 transition-all duration-200"
                      >
                        {entity.status === "syncing" ? (
                          <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                        ) : (
                          <Play className="h-3 w-3 mr-1" />
                        )}
                        {entity.status === "syncing" ? "Syncing..." : "Sync"}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Settings;
