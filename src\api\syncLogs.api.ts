// Sync Logs API service functions

import { apiClient, handleApiError } from './axios.config';
import { SyncLog, SyncLogFilters } from '../store/types/syncLogs.types';

/**
 * Sync log query parameters for company-specific logs
 */
export interface CompanySyncLogFilters {
  companyId: string;
  startDate?: string;
  endDate?: string;
  entity?: string;
  status?: string;
  integration?: string;
  limit?: number;
  offset?: number;
  page?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Sync statistics for a company
 */
export interface CompanySyncStats {
  companyId: string;
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  pendingSyncs: number;
  lastSyncAt?: string;
  averageDuration: number;
  syncsByEntity: Record<string, number>;
  syncsByStatus: Record<string, number>;
}

/**
 * Detailed sync log with full information
 */
export interface DetailedSyncLog extends SyncLog {
  companyId: string;
  companyName: string;
  entity: string;
  integration: string;
  requestPayload?: any;
  responsePayload?: any;
  errorDetails?: string;
  retryCount: number;
  maxRetries: number;
}

// Sync Logs API endpoints
export const syncLogsApi = {
  /**
   * Get sync logs with filtering & pagination for a specific company
   * GET /api/v1/companies/:companyId/sync-logs
   */
  getCompanySyncLogs: async (filters: CompanySyncLogFilters): Promise<{
    items: SyncLog[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> => {
    try {
      const { companyId, ...params } = filters;
      const response = await apiClient.get(`/companies/${companyId}/sync-logs`, {
        params,
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Get detailed sync log by ID
   * GET /api/v1/sync-logs/:syncLogId
   */
  getSyncLogDetails: async (syncLogId: string): Promise<DetailedSyncLog> => {
    try {
      const response = await apiClient.get<DetailedSyncLog>(`/sync-logs/${syncLogId}`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Retry failed sync operation
   * POST /api/v1/sync-logs/:syncLogId/retry
   */
  retrySyncOperation: async (syncLogId: string): Promise<{ success: boolean; message: string; newSyncLogId?: string }> => {
    try {
      const response = await apiClient.post<{ success: boolean; message: string; newSyncLogId?: string }>(`/sync-logs/${syncLogId}/retry`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Get sync statistics for company
   * GET /api/v1/companies/:companyId/sync-stats
   */
  getCompanySyncStats: async (companyId: string): Promise<CompanySyncStats> => {
    try {
      const response = await apiClient.get<CompanySyncStats>(`/companies/${companyId}/sync-stats`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  // Retry a failed sync operation
  retrySyncOperation: async (params: {
    logId: string;
    endpoint: string;
  }): Promise<SyncLog> => {
    try {
      const response = await apiClient.post(`/sync-logs/${params.logId}/retry`, {
        endpoint: params.endpoint,
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Clear all sync logs
  clearSyncLogs: async (): Promise<{ success: boolean }> => {
    try {
      const response = await apiClient.delete('/sync-logs');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Export sync logs
  exportSyncLogs: async (filters?: Partial<SyncLogFilters>): Promise<{
    downloadUrl: string;
    filename: string;
  }> => {
    try {
      const params = new URLSearchParams();

      if (filters?.endpoint && filters.endpoint !== 'all') {
        params.append('endpoint', filters.endpoint);
      }
      if (filters?.status && filters.status !== 'all') {
        params.append('status', filters.status);
      }
      if (filters?.searchQuery) {
        params.append('search', filters.searchQuery);
      }
      if (filters?.dateRange?.start) {
        params.append('startDate', filters.dateRange.start);
      }
      if (filters?.dateRange?.end) {
        params.append('endDate', filters.dateRange.end);
      }

      const response = await apiClient.post(`/sync-logs/export?${params.toString()}`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Get sync log details by ID
  getSyncLogDetails: async (logId: string): Promise<SyncLog> => {
    try {
      const response = await apiClient.get(`/sync-logs/${logId}`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Get sync statistics
  getSyncStatistics: async (filters?: {
    startDate?: string;
    endDate?: string;
    organizationId?: string;
  }): Promise<{
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
    averageDuration: string;
    syncsByEndpoint: Record<string, number>;
    syncsByStatus: Record<string, number>;
  }> => {
    try {
      const params = new URLSearchParams();

      if (filters?.startDate) {
        params.append('startDate', filters.startDate);
      }
      if (filters?.endDate) {
        params.append('endDate', filters.endDate);
      }
      if (filters?.organizationId) {
        params.append('organizationId', filters.organizationId);
      }

      const response = await apiClient.get(`/sync-logs/statistics?${params.toString()}`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },
};

export default syncLogsApi;
