// Sync Utilities
// Helper functions and examples for sync operations

import { SY<PERSON>_ENTITIES, SYNC_PRIORITIES, SyncTriggerRequestData } from '../lib/validation';

/**
 * Create a sync request for a single entity
 * @param companyId - UUID of the company
 * @param entityName - Name of the entity to sync
 * @param options - Additional sync options
 * @returns Validated sync request object
 */
export const createSingleEntitySyncRequest = (
  companyId: string,
  entityName: typeof SYNC_ENTITIES[number],
  options: {
    priority?: 'HIGH' | 'NORMAL' | 'LOW';
    fullSync?: boolean;
  } = {}
): SyncTriggerRequestData => {
  return {
    companyId,
    entities: [entityName],
    priority: options.priority || 'NORMAL',
    fullSync: options.fullSync || false,
  };
};

/**
 * Create a sync request for multiple entities
 * @param companyId - UUID of the company
 * @param entityNames - Array of entity names to sync
 * @param options - Additional sync options
 * @returns Validated sync request object
 */
export const createMultiEntitySyncRequest = (
  companyId: string,
  entityNames: (typeof SYNC_ENTITIES[number])[],
  options: {
    priority?: 'HIGH' | 'NORMAL' | 'LOW';
    fullSync?: boolean;
  } = {}
): SyncTriggerRequestData => {
  return {
    companyId,
    entities: entityNames,
    priority: options.priority || 'NORMAL',
    fullSync: options.fullSync || false,
  };
};

/**
 * Create a full sync request for all entities
 * @param companyId - UUID of the company
 * @param options - Additional sync options
 * @returns Validated sync request object
 */
export const createFullSyncRequest = (
  companyId: string,
  options: {
    priority?: 'HIGH' | 'NORMAL' | 'LOW';
    entities?: (typeof SYNC_ENTITIES[number])[];
  } = {}
): SyncTriggerRequestData => {
  return {
    companyId,
    entities: options.entities || [...SYNC_ENTITIES],
    priority: options.priority || 'NORMAL',
    fullSync: true,
  };
};

/**
 * Example usage for syncing a single entity
 */
export const syncSingleEntityExample = () => {
  const companyId = "123e4567-e89b-12d3-a456-************"; // Example UUID
  
  // Sync only Accounts entity with normal priority
  const accountsSync = createSingleEntitySyncRequest(companyId, 'Accounts');
  
  // Sync Bank Transactions with high priority and full sync
  const bankTransactionsSync = createSingleEntitySyncRequest(
    companyId, 
    'Bank Transactions',
    { priority: 'HIGH', fullSync: true }
  );
  
  return { accountsSync, bankTransactionsSync };
};

/**
 * Example usage for syncing multiple entities
 */
export const syncMultipleEntitiesExample = () => {
  const companyId = "123e4567-e89b-12d3-a456-************"; // Example UUID
  
  // Sync financial entities with high priority
  const financialEntitiesSync = createMultiEntitySyncRequest(
    companyId,
    ['Accounts', 'Bank Transactions', 'Invoices', 'Payments'],
    { priority: 'HIGH', fullSync: false }
  );
  
  // Sync contact and item entities with normal priority
  const contactItemSync = createMultiEntitySyncRequest(
    companyId,
    ['Contacts', 'Items'],
    { priority: 'NORMAL', fullSync: true }
  );
  
  return { financialEntitiesSync, contactItemSync };
};

/**
 * Example usage for full sync
 */
export const syncAllEntitiesExample = () => {
  const companyId = "123e4567-e89b-12d3-a456-************"; // Example UUID
  
  // Full sync of all entities with high priority
  const fullSyncAll = createFullSyncRequest(companyId, { priority: 'HIGH' });
  
  // Full sync of specific entities only
  const fullSyncSpecific = createFullSyncRequest(
    companyId,
    { 
      priority: 'NORMAL',
      entities: ['Accounts', 'Contacts', 'Invoices', 'Payments']
    }
  );
  
  return { fullSyncAll, fullSyncSpecific };
};

/**
 * Validate if an entity name is supported
 * @param entityName - Entity name to validate
 * @returns boolean indicating if entity is supported
 */
export const isValidSyncEntity = (entityName: string): entityName is typeof SYNC_ENTITIES[number] => {
  return SYNC_ENTITIES.includes(entityName as any);
};

/**
 * Get all available sync entities
 * @returns Array of all supported sync entities
 */
export const getAllSyncEntities = () => {
  return [...SYNC_ENTITIES];
};

/**
 * Get all available sync priorities
 * @returns Array of all supported sync priorities
 */
export const getAllSyncPriorities = () => {
  return [...SYNC_PRIORITIES];
};

/**
 * Filter entities by category (example categorization)
 */
export const getEntitiesByCategory = () => {
  return {
    financial: ['Accounts', 'Bank Transactions', 'Bank Transfers', 'Invoices', 'Payments', 'Receipts'],
    inventory: ['Items', 'Purchase Orders', 'Quotes'],
    contacts: ['Contacts'],
    reporting: ['Reports (P&L, Balance Sheet, Trial Balance)', 'Budgets'],
    configuration: ['Tax Rates', 'Tracking Categories'],
    documents: ['Attachments', 'Credit Notes', 'Repeating Invoices'],
    journals: ['Journals', 'Manual Journals']
  };
};

/**
 * Create sync request with validation
 * @param request - Raw sync request data
 * @returns Validated sync request or throws error
 */
export const validateSyncRequest = (request: any): SyncTriggerRequestData => {
  // This would typically use the Zod schema for validation
  // For now, we'll do basic validation
  if (!request.companyId) {
    throw new Error('Company ID is required');
  }
  
  if (!request.entities || !Array.isArray(request.entities) || request.entities.length === 0) {
    throw new Error('At least one entity must be specified');
  }
  
  // Validate entities
  const invalidEntities = request.entities.filter((entity: string) => !isValidSyncEntity(entity));
  if (invalidEntities.length > 0) {
    throw new Error(`Invalid entities: ${invalidEntities.join(', ')}`);
  }
  
  return {
    companyId: request.companyId,
    entities: request.entities,
    priority: request.priority || 'NORMAL',
    fullSync: request.fullSync || false,
  };
};

// Export constants for easy access
export { SYNC_ENTITIES, SYNC_PRIORITIES };
export type { SyncTriggerRequestData };
