import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { SettingsState, SyncEntity, ApiError } from '../types';

// Mock data
const mockEntities: SyncEntity[] = [
  { name: 'Accounts', lastSync: '2024-06-12 10:30:45', status: 'success', recordCount: 45, enabled: true },
  { name: 'Bank Transactions', lastSync: '2024-06-12 10:25:12', status: 'success', recordCount: 128, enabled: true },
  { name: 'Bank Transfers', lastSync: '2024-06-12 09:45:33', status: 'success', recordCount: 23, enabled: true },
  { name: 'Budgets', lastSync: '2024-06-11 12:00:00', status: 'success', recordCount: 5, enabled: true },
  { name: 'Contacts', lastSync: '2024-06-11 12:00:00', status: 'success', recordCount: 67, enabled: true },
  { name: 'Credit Notes', lastSync: '2024-06-12 09:30:18', status: 'success', recordCount: 12, enabled: true },
  { name: 'Currencies', lastSync: '2024-06-11 12:00:00', status: 'success', recordCount: 3, enabled: true },
  { name: 'Employees', lastSync: '2024-06-11 12:00:00', status: 'success', recordCount: 15, enabled: true },
  { name: 'Expense Claims', lastSync: '2024-06-12 08:15:22', status: 'success', recordCount: 8, enabled: true },
  { name: 'Invoices', lastSync: '2024-06-12 10:20:33', status: 'success', recordCount: 89, enabled: true },
  { name: 'Journals', lastSync: '2024-06-12 09:00:11', status: 'success', recordCount: 23, enabled: true },
  { name: 'Manual Journals', lastSync: '2024-06-12 08:45:15', status: 'success', recordCount: 7, enabled: true },
  { name: 'Payments', lastSync: '2024-06-12 09:45:21', status: 'success', recordCount: 45, enabled: true },
  { name: 'Tracking Categories', lastSync: '2024-06-12 08:45:33', status: 'success', recordCount: 6, enabled: true },
  { name: 'Tax Rates', lastSync: '2024-06-12 09:15:42', status: 'success', recordCount: 8, enabled: true },
  { name: 'Attachments', lastSync: '2024-06-11 12:00:00', status: 'success', recordCount: 34, enabled: true },
  { name: 'Reports (P&L, Balance Sheet, Trial Balance)', lastSync: '2024-06-12 10:35:00', status: 'success', recordCount: 3, enabled: true }
];

// Initial state
const initialState: SettingsState = {
  entities: mockEntities,
  syncSchedule: {
    enabled: true,
    frequency: 'daily',
    time: '18:00',
  },
  isLoading: false,
  error: null,
};

// Async thunks
export const syncEntity = createAsyncThunk(
  'settings/syncEntity',
  async (entityName: string, { rejectWithValue }) => {
    try {
      // Simulate sync operation
      await new Promise(resolve => setTimeout(resolve, 2000));


      const currentTime = new Date().toLocaleString('en-CA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(',', '');

      return {
        entityName,
        lastSync: currentTime,
        status: 'success' as const,
        recordCount: Math.floor(Math.random() * 100) + 10 // Random count for demo
      };
    } catch (error) {
      return rejectWithValue({
        message: `Failed to sync ${entityName}`,
        code: 'SYNC_ENTITY_FAILED'
      } as ApiError);
    }
  }
);

export const syncAllEntities = createAsyncThunk(
  'settings/syncAllEntities',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { settings: SettingsState };
      const enabledEntities = state.settings.entities.filter(entity => entity.enabled);

      // Simulate syncing all entities
      await new Promise(resolve => setTimeout(resolve, 3000));


      const currentTime = new Date().toLocaleString('en-CA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(',', '');

      return {
        syncTime: currentTime,
        entityCount: enabledEntities.length
      };
    } catch (error) {
      return rejectWithValue({
        message: 'Failed to sync all entities',
        code: 'SYNC_ALL_FAILED'
      } as ApiError);
    }
  }
);

export const updateSyncSchedule = createAsyncThunk(
  'settings/updateSyncSchedule',
  async (schedule: SettingsState['syncSchedule'], { rejectWithValue }) => {
    try {
      // Simulate API call to update schedule
      await new Promise(resolve => setTimeout(resolve, 500));


      return schedule;
    } catch (error) {
      return rejectWithValue({
        message: 'Failed to update sync schedule',
        code: 'UPDATE_SCHEDULE_FAILED'
      } as ApiError);
    }
  }
);

export const fetchSyncSettings = createAsyncThunk(
  'settings/fetchSyncSettings',
  async (_, { rejectWithValue }) => {
    try {
      const { settingsApi } = await import('../../api/settings.api');

      const response = await settingsApi.fetchSyncSettings();
      return {
        entities: response.entities,
        syncSchedule: {
          enabled: response.globalSettings.autoSync,
          frequency: 'daily' as const,
          time: '18:00',
        }
      };
    } catch (error) {
      return rejectWithValue({
        message: error instanceof Error ? error.message : 'Failed to fetch sync settings',
        code: 'FETCH_SETTINGS_FAILED'
      } as ApiError);
    }
  }
);

// Slice
const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    toggleEntityEnabled: (state, action: PayloadAction<string>) => {
      const entity = state.entities.find(e => e.name === action.payload);
      if (entity) {
        entity.enabled = !entity.enabled;
      }
    },
    updateEntityStatus: (state, action: PayloadAction<{ name: string; status: SyncEntity['status'] }>) => {
      const entity = state.entities.find(e => e.name === action.payload.name);
      if (entity) {
        entity.status = action.payload.status;
      }
    },
    setSyncSchedule: (state, action: PayloadAction<SettingsState['syncSchedule']>) => {
      state.syncSchedule = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetSettings: (state) => {
      state.entities = mockEntities;
      state.syncSchedule = {
        enabled: true,
        frequency: 'daily',
        time: '18:00',
      };
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Sync single entity
    builder
      .addCase(syncEntity.pending, (state, action) => {
        const entity = state.entities.find(e => e.name === action.meta.arg);
        if (entity) {
          entity.status = 'syncing';
        }
        state.error = null;
      })
      .addCase(syncEntity.fulfilled, (state, action) => {
        const entity = state.entities.find(e => e.name === action.payload.entityName);
        if (entity) {
          entity.status = action.payload.status;
          entity.lastSync = action.payload.lastSync;
          entity.recordCount = action.payload.recordCount;
        }
      })
      .addCase(syncEntity.rejected, (state, action) => {
        const entityName = action.meta.arg;
        const entity = state.entities.find(e => e.name === entityName);
        if (entity) {
          entity.status = 'pending';
        }
        state.error = action.payload?.message || `Failed to sync ${entityName}`;
      });

    // Sync all entities
    builder
      .addCase(syncAllEntities.pending, (state) => {
        state.isLoading = true;
        state.entities.forEach(entity => {
          if (entity.enabled) {
            entity.status = 'syncing';
          }
        });
        state.error = null;
      })
      .addCase(syncAllEntities.fulfilled, (state, action) => {
        state.isLoading = false;
        state.entities.forEach(entity => {
          if (entity.enabled) {
            entity.status = 'success';
            entity.lastSync = action.payload.syncTime;
          }
        });
      })
      .addCase(syncAllEntities.rejected, (state, action) => {
        state.isLoading = false;
        state.entities.forEach(entity => {
          if (entity.status === 'syncing') {
            entity.status = 'pending';
          }
        });
        state.error = action.payload?.message || 'Failed to sync all entities';
      });

    // Update sync schedule
    builder
      .addCase(updateSyncSchedule.fulfilled, (state, action) => {
        state.syncSchedule = action.payload;
      })
      .addCase(updateSyncSchedule.rejected, (state, action) => {
        state.error = action.payload?.message || 'Failed to update sync schedule';
      });

    // Fetch sync settings
    builder
      .addCase(fetchSyncSettings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSyncSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.entities = action.payload.entities;
        state.syncSchedule = action.payload.syncSchedule;
      })
      .addCase(fetchSyncSettings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || 'Failed to fetch sync settings';
      });
  },
});

export const {
  toggleEntityEnabled,
  updateEntityStatus,
  setSyncSchedule,
  clearError,
  resetSettings
} = settingsSlice.actions;

export default settingsSlice.reducer;
